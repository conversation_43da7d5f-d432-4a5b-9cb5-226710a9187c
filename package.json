{"name": "mis-vue3", "version": "0.0.1", "private": true, "scripts": {"serve": "vite", "build-ci": "vite build --mode ci", "build": "vite build", "preview": "vite preview", "test": "vue-tsc", "format": "prettier --write src/", "prepare": "husky install"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@gt/mis-components-web": "1.0.91", "ace-builds": "^1.32.8", "ant-design-vue": "4.2.6", "axios": "^1.4.0", "cesium": "^1.131.0", "crypto-js": "^4.1.1", "file-saver": "^2.0.5", "lodash": "4.17.21", "mapbox-gl": "1.13.0", "md5": "2.3.0", "mitt": "3.0.0", "nanoid": "5.0.6", "pinia": "2.1.3", "vue": "^3.4.0", "vue-router": "^4.2.2", "vue3-ace-editor": "2.2.4"}, "devDependencies": {"@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@rushstack/eslint-patch": "^1.2.0", "@tsconfig/node18": "^2.0.1", "@types/js-md5": "^0.7.0", "@types/jsdom": "^21.1.1", "@types/lodash": "^4.14.195", "@types/mapbox-gl": "^3.4.0", "@types/node": "^18.16.17", "@typescript-eslint/eslint-plugin": "^4.21.0", "@typescript-eslint/parser": "^4.21.0", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.3.2", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.20", "eslint": "^7.32.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.15.1", "husky": "^9.0.11", "jsdom": "^22.1.0", "less": "^4.1.3", "lint-staged": "^10.5.4", "npm-run-all": "^4.1.5", "postcss": "^8.4.47", "prettier": "^3.0.0", "tailwindcss": "^3.4.13", "typescript": "~5.0.4", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.1", "vite": "^4.3.9", "vite-plugin-vue-setup-extend": "^0.4.0", "vitest": "^0.32.0", "vue-tsc": "^1.6.5"}}