<template>
  <div id="cesiumContainer" class="cesium-container"></div>
</template>

<script lang="ts" setup>
import * as Cesium from 'cesium'

onMounted(() => {
  Cesium.Ion.defaultAccessToken =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.Trh3Rr_1gX3oh-ERhIbFrUGvVYwDJCMP1b16r6uv80E'

  // 使用 Mapbox 作为影像图层
  const mapboxImageryProvider = new Cesium.MapboxStyleImageryProvider({
    styleId: 'mapbox/streets-v11', // 或其他样式
    accessToken: 'your_mapbox_token'
  })

  const viewer = new Cesium.Viewer('cesiumContainer', {
    imageryProvider: mapboxImageryProvider,
    terrainProvider: new Cesium.EllipsoidTerrainProvider(),
    timeline: false,
    animation: false,
    homeButton: false,
    sceneModePicker: false,
    baseLayerPicker: false,
    navigationHelpButton: false,
    fullscreenButton: false,
    vrButton: false
  })

  // 设置初始视角（可选）
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(116.4074, 39.9042, 1000) // 北京
  })
})
</script>

<style scoped>
.cesium-container {
  width: 100%;
  height: 100vh;
}
</style>
