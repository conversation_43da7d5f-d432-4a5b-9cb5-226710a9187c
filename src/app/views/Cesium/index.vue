<template>
  <div id="cesiumContainer"></div>
</template>
<script lang="ts" setup>
import {
  Cartesian3,
  createOsmBuildingsAsync,
  Ion,
  Math as CesiumMath,
  Terrain,
  Viewer
} from 'cesium'
import 'cesium/Build/Cesium/Widgets/widgets.css'

Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI4MWFjNTY5ZC03ZGU0LTQwZjAtODBmNi05ZjQ4MGM4YzgyMDYiLCJpZCI6MTk3ODA0LCJpYXQiOjE3NTM5NDgwNDF9.Trh3Rr_1gX3oh-ERhIbFrUGvVYwDJCMP1b16r6uv80E'

// Initialize the Cesium Viewer in the HTML element with the `cesiumContainer` ID.
const viewer = new Viewer('cesiumContainer', {
  terrain: Terrain.fromWorldTerrain()
})

// Fly the camera to San Francisco at the given longitude, latitude, and height.
viewer.camera.flyTo({
  destination: Cartesian3.fromDegrees(-122.4175, 37.655, 400),
  orientation: {
    heading: CesiumMath.toRadians(0.0),
    pitch: CesiumMath.toRadians(-15.0)
  }
})

// Add Cesium OSM Buildings, a global 3D buildings layer.
const buildingTileset = await createOsmBuildingsAsync()
viewer.scene.primitives.add(buildingTileset)
</script>
