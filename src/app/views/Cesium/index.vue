<template>
  <div class="cesium-mapbox-demo">
    <div class="demo-header">
      <h1>Cesium & Mapbox 集成演示</h1>
      <div class="demo-controls">
        <a-button @click="showCesium" :type="currentView === 'cesium' ? 'primary' : 'default'">
          Cesium 3D 地球
        </a-button>
        <a-button @click="showMapbox" :type="currentView === 'mapbox' ? 'primary' : 'default'">
          Mapbox 2D 地图
        </a-button>
        <a-button @click="showBoth" :type="currentView === 'both' ? 'primary' : 'default'">
          同时显示
        </a-button>
      </div>
    </div>

    <div class="demo-content">
      <!-- Cesium 容器 -->
      <div
        v-show="currentView === 'cesium' || currentView === 'both'"
        ref="cesiumContainer"
        class="map-container cesium-container"
        :class="{ 'half-width': currentView === 'both' }"
      ></div>

      <!-- Mapbox 容器 -->
      <div
        v-show="currentView === 'mapbox' || currentView === 'both'"
        ref="mapboxContainer"
        class="map-container mapbox-container"
        :class="{ 'half-width': currentView === 'both' }"
      ></div>
    </div>

    <!-- 功能演示面板 -->
    <div class="demo-panel">
      <a-card title="功能演示" size="small">
        <div class="feature-buttons">
          <a-button @click="addCesiumEntity" :disabled="!cesiumViewer"> 添加 Cesium 实体 </a-button>
          <a-button @click="addMapboxMarker" :disabled="!mapboxMap"> 添加 Mapbox 标记 </a-button>
          <a-button @click="flyToLocation"> 飞行到北京 </a-button>
          <a-button @click="toggleTerrain" :disabled="!cesiumViewer"> 切换地形 </a-button>
          <a-button @click="changeMapboxStyle" :disabled="!mapboxMap"> 切换地图样式 </a-button>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as Cesium from 'cesium'
import mapboxgl from 'mapbox-gl'
import 'cesium/Build/Cesium/Widgets/widgets.css'
import 'mapbox-gl/dist/mapbox-gl.css'

// 响应式数据
const cesiumContainer = ref<HTMLElement>()
const mapboxContainer = ref<HTMLElement>()
const currentView = ref<'cesium' | 'mapbox' | 'both'>('cesium')
const cesiumViewer = ref<Cesium.Viewer | null>(null)
const mapboxMap = ref<mapboxgl.Map | null>(null)

// Cesium Ion token
Cesium.Ion.defaultAccessToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI4MWFjNTY5ZC03ZGU0LTQwZjAtODBmNi05ZjQ4MGM4YzgyMDYiLCJpZCI6MTk3ODA0LCJpYXQiOjE3NTM5NDgwNDF9.Trh3Rr_1gX3oh-ERhIbFrUGvVYwDJCMP1b16r6uv80E'

// Mapbox access token (使用公共token，生产环境请替换为你的token)
const MAPBOX_ACCESS_TOKEN =
  'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw'

// 地图样式列表
const mapboxStyles = [
  'mapbox://styles/mapbox/streets-v11',
  'mapbox://styles/mapbox/satellite-v9',
  'mapbox://styles/mapbox/dark-v10',
  'mapbox://styles/mapbox/light-v10'
]
let currentStyleIndex = 0
let terrainEnabled = true

// 初始化 Cesium
const initCesium = () => {
  if (!cesiumContainer.value) return

  cesiumViewer.value = new Cesium.Viewer(cesiumContainer.value, {
    terrain: Cesium.Terrain.fromWorldTerrain(),
    homeButton: false,
    sceneModePicker: false,
    baseLayerPicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: false,
    vrButton: false,
    geocoder: false,
    infoBox: false,
    selectionIndicator: false
  })

  // 设置初始视角到中国
  cesiumViewer.value.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(116.4074, 39.9042, 10000000), // 北京上空
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-90),
      roll: 0.0
    }
  })

  // 添加一些示例数据
  addSampleCesiumData()
}

// 初始化 Mapbox
const initMapbox = () => {
  if (!mapboxContainer.value) return

  mapboxgl.accessToken = MAPBOX_ACCESS_TOKEN

  mapboxMap.value = new mapboxgl.Map({
    container: mapboxContainer.value,
    style: mapboxStyles[0],
    center: [116.4074, 39.9042], // 北京
    zoom: 10
  })

  // 添加导航控件
  mapboxMap.value.addControl(new mapboxgl.NavigationControl())

  // 添加一些示例数据
  mapboxMap.value.on('load', () => {
    addSampleMapboxData()
  })
}

// 添加示例 Cesium 数据
const addSampleCesiumData = () => {
  if (!cesiumViewer.value) return

  // 添加一个红色的盒子
  cesiumViewer.value.entities.add({
    name: '北京天安门',
    position: Cesium.Cartesian3.fromDegrees(116.3974, 39.9093, 100),
    box: {
      dimensions: new Cesium.Cartesian3(1000, 1000, 500),
      material: Cesium.Color.RED.withAlpha(0.8),
      outline: true,
      outlineColor: Cesium.Color.BLACK
    }
  })

  // 添加一个标签
  cesiumViewer.value.entities.add({
    name: '故宫',
    position: Cesium.Cartesian3.fromDegrees(116.3972, 39.9163, 0),
    label: {
      text: '故宫博物院',
      font: '14pt monospace',
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      outlineWidth: 2,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0, -9)
    }
  })
}

// 添加示例 Mapbox 数据
const addSampleMapboxData = () => {
  if (!mapboxMap.value) return

  // 添加标记
  new mapboxgl.Marker({ color: 'red' })
    .setLngLat([116.3974, 39.9093])
    .setPopup(new mapboxgl.Popup().setHTML('<h3>天安门广场</h3><p>中华人民共和国的象征</p>'))
    .addTo(mapboxMap.value)

  new mapboxgl.Marker({ color: 'blue' })
    .setLngLat([116.3972, 39.9163])
    .setPopup(new mapboxgl.Popup().setHTML('<h3>故宫博物院</h3><p>明清两朝的皇家宫殿</p>'))
    .addTo(mapboxMap.value)
}

// 视图切换方法
const showCesium = () => {
  currentView.value = 'cesium'
  if (!cesiumViewer.value) {
    nextTick(() => initCesium())
  }
}

const showMapbox = () => {
  currentView.value = 'mapbox'
  if (!mapboxMap.value) {
    nextTick(() => initMapbox())
  }
}

const showBoth = () => {
  currentView.value = 'both'
  if (!cesiumViewer.value) {
    nextTick(() => initCesium())
  }
  if (!mapboxMap.value) {
    nextTick(() => initMapbox())
  }
}

// 功能演示方法
const addCesiumEntity = () => {
  if (!cesiumViewer.value) return

  const randomLon = 116.4074 + (Math.random() - 0.5) * 0.1
  const randomLat = 39.9042 + (Math.random() - 0.5) * 0.1

  cesiumViewer.value.entities.add({
    name: `随机实体 ${Date.now()}`,
    position: Cesium.Cartesian3.fromDegrees(randomLon, randomLat, 1000),
    cylinder: {
      length: 2000,
      topRadius: 500,
      bottomRadius: 500,
      material: Cesium.Color.fromRandom({ alpha: 0.8 })
    }
  })
}

const addMapboxMarker = () => {
  if (!mapboxMap.value) return

  const randomLon = 116.4074 + (Math.random() - 0.5) * 0.1
  const randomLat = 39.9042 + (Math.random() - 0.5) * 0.1

  new mapboxgl.Marker({ color: `#${Math.floor(Math.random() * 16777215).toString(16)}` })
    .setLngLat([randomLon, randomLat])
    .setPopup(
      new mapboxgl.Popup().setHTML(
        `<h3>随机标记</h3><p>位置: ${randomLon.toFixed(4)}, ${randomLat.toFixed(4)}</p>`
      )
    )
    .addTo(mapboxMap.value)
}

const flyToLocation = () => {
  const beijingLon = 116.4074
  const beijingLat = 39.9042

  if (cesiumViewer.value) {
    cesiumViewer.value.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(beijingLon, beijingLat, 50000),
      duration: 3
    })
  }

  if (mapboxMap.value) {
    mapboxMap.value.flyTo({
      center: [beijingLon, beijingLat],
      zoom: 12,
      duration: 3000
    })
  }
}

const toggleTerrain = async () => {
  if (!cesiumViewer.value) return

  terrainEnabled = !terrainEnabled
  if (terrainEnabled) {
    cesiumViewer.value.scene.terrainProvider = await Cesium.createWorldTerrainAsync()
  } else {
    cesiumViewer.value.scene.terrainProvider = new Cesium.EllipsoidTerrainProvider()
  }
}

const changeMapboxStyle = () => {
  if (!mapboxMap.value) return

  currentStyleIndex = (currentStyleIndex + 1) % mapboxStyles.length
  mapboxMap.value.setStyle(mapboxStyles[currentStyleIndex])
}

// 生命周期钩子
onMounted(() => {
  // 默认初始化 Cesium
  nextTick(() => initCesium())
})

onUnmounted(() => {
  // 清理资源
  if (cesiumViewer.value) {
    cesiumViewer.value.destroy()
  }
  if (mapboxMap.value) {
    mapboxMap.value.remove()
  }
})
</script>

<style scoped>
.cesium-mapbox-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.demo-header {
  padding: 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #d9d9d9;
}

.demo-header h1 {
  margin: 0 0 16px 0;
  font-size: 24px;
  color: #1890ff;
}

.demo-controls {
  display: flex;
  gap: 8px;
}

.demo-content {
  flex: 1;
  display: flex;
  position: relative;
}

.map-container {
  width: 100%;
  height: 100%;
}

.half-width {
  width: 50%;
}

.cesium-container {
  border-right: 1px solid #d9d9d9;
}

.demo-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1000;
  max-width: 300px;
}

.feature-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-buttons .ant-btn {
  text-align: left;
}

/* Cesium 样式覆盖 */
:deep(.cesium-viewer-bottom) {
  display: none;
}

:deep(.cesium-widget-credits) {
  display: none;
}

/* Mapbox 样式覆盖 */
:deep(.mapboxgl-ctrl-bottom-left),
:deep(.mapboxgl-ctrl-bottom-right) {
  display: none;
}
</style>
